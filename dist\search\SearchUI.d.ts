import { SearchEngine } from './SearchEngine';
export declare class SearchUI {
    private searchEngine;
    private suggestionContainer;
    private currentSuggestionIndex;
    private suggestions;
    private debounceTimer;
    constructor(searchEngine: SearchEngine);
    private initializeUI;
    private createSuggestionContainer;
    private createSearchProviderSelector;
    private generateProviderOptions;
    private updateProviderSelector;
    private setupEventListeners;
    private handleInput;
    private handleKeyDown;
    private navigateSuggestions;
    private selectCurrentSuggestion;
    private performSearch;
    private renderSuggestions;
    private getSuggestionIcon;
    private showSuggestions;
    private hideSuggestions;
    private updateProviderInfo;
    private updateToggleStates;
    private toggleSearchSuggestions;
    private toggleSearchHistory;
    private escapeHtml;
    destroy(): void;
}
//# sourceMappingURL=SearchUI.d.ts.map