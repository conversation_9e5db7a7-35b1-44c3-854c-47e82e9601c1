{"extends": "./tsconfig.json", "compilerOptions": {"sourceMap": false, "declaration": false, "removeComments": true, "noEmitOnError": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "useDefineForClassFields": false, "importHelpers": false, "downlevelIteration": false, "noEmitHelpers": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "release", "build", "executable", "executable-working", "executable-fixed", "dist-test", "**/*.test.ts", "**/*.spec.ts", "test-*.ts", "**/*.d.ts"]}