#!/usr/bin/env node

/**
 * Optimized Production Build Script for Phantom Browser
 * Addresses recursive copying, file bloat, and performance issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

class OptimizedBuilder {
    constructor() {
        this.startTime = performance.now();
        this.buildMetrics = {
            typeScriptTime: 0,
            packagingTime: 0,
            optimizationTime: 0,
            totalSize: 0,
            excludedFiles: 0
        };
    }

    async build() {
        console.log('🚀 Starting Optimized Phantom Browser Build...\n');

        try {
            await this.cleanPreviousBuilds();
            await this.compileTypeScript();
            await this.optimizeAssets();
            await this.packageApplication();
            await this.validateBuild();
            this.generateBuildReport();
        } catch (error) {
            console.error('❌ Build failed:', error.message);
            process.exit(1);
        }
    }

    async cleanPreviousBuilds() {
        console.log('🧹 Cleaning previous builds...');
        const dirsToClean = ['dist', 'release', 'build'];

        for (const dir of dirsToClean) {
            if (fs.existsSync(dir)) {
                try {
                    // Try multiple times to handle locked files
                    let attempts = 0;
                    const maxAttempts = 3;

                    while (attempts < maxAttempts) {
                        try {
                            fs.rmSync(dir, { recursive: true, force: true });
                            console.log(`  ✅ Cleaned ${dir}/`);
                            break;
                        } catch (error) {
                            attempts++;
                            if (attempts >= maxAttempts) {
                                console.warn(`  ⚠️  Could not fully clean ${dir}/ (files may be locked)`);
                                // Try to clean individual files
                                this.forceCleanDirectory(dir);
                            } else {
                                console.log(`  🔄 Retrying cleanup of ${dir}/ (attempt ${attempts + 1}/${maxAttempts})`);
                                await new Promise(resolve => setTimeout(resolve, 1000));
                            }
                        }
                    }
                } catch (error) {
                    console.warn(`  ⚠️  Warning: Could not clean ${dir}/: ${error.message}`);
                }
            }
        }
    }

    forceCleanDirectory(dirPath) {
        try {
            const files = fs.readdirSync(dirPath);
            for (const file of files) {
                const fullPath = path.join(dirPath, file);
                try {
                    const stat = fs.statSync(fullPath);
                    if (stat.isDirectory()) {
                        this.forceCleanDirectory(fullPath);
                        fs.rmdirSync(fullPath);
                    } else {
                        fs.unlinkSync(fullPath);
                    }
                } catch (error) {
                    // Skip locked files
                    console.log(`    Skipping locked file: ${file}`);
                }
            }
        } catch (error) {
            // Directory might not exist or be accessible
        }
    }

    async compileTypeScript() {
        console.log('🔨 Compiling TypeScript...');
        const startTime = performance.now();

        try {
            execSync('npx tsc --project tsconfig.json', { 
                stdio: 'inherit',
                env: { ...process.env, NODE_ENV: 'production' }
            });
            
            this.buildMetrics.typeScriptTime = performance.now() - startTime;
            console.log(`  ✅ TypeScript compiled in ${this.buildMetrics.typeScriptTime.toFixed(2)}ms`);
        } catch (error) {
            throw new Error(`TypeScript compilation failed: ${error.message}`);
        }
    }

    async optimizeAssets() {
        console.log('⚡ Optimizing assets...');
        const startTime = performance.now();

        // Remove source maps and type definitions from dist
        this.removeUnnecessaryFiles('dist', ['.map', '.d.ts']);
        
        // Optimize renderer assets
        if (fs.existsSync('renderer')) {
            this.optimizeRendererAssets();
        }

        this.buildMetrics.optimizationTime = performance.now() - startTime;
        console.log(`  ✅ Assets optimized in ${this.buildMetrics.optimizationTime.toFixed(2)}ms`);
    }

    removeUnnecessaryFiles(directory, extensions) {
        const files = fs.readdirSync(directory, { withFileTypes: true });
        
        for (const file of files) {
            const fullPath = path.join(directory, file.name);
            
            if (file.isDirectory()) {
                this.removeUnnecessaryFiles(fullPath, extensions);
            } else {
                const shouldRemove = extensions.some(ext => file.name.endsWith(ext));
                if (shouldRemove) {
                    fs.unlinkSync(fullPath);
                    this.buildMetrics.excludedFiles++;
                }
            }
        }
    }

    optimizeRendererAssets() {
        // Minify renderer JavaScript if needed
        const rendererJs = path.join('renderer', 'renderer.js');
        if (fs.existsSync(rendererJs)) {
            console.log('  📦 Optimizing renderer assets...');
            // Add minification logic here if needed
        }
    }

    async packageApplication() {
        console.log('📦 Packaging application...');
        const startTime = performance.now();

        try {
            // Use electron-builder with optimized configuration
            execSync('npx electron-builder --win --x64 --dir', { 
                stdio: 'inherit',
                env: { 
                    ...process.env, 
                    NODE_ENV: 'production',
                    DEBUG: ''
                }
            });

            this.buildMetrics.packagingTime = performance.now() - startTime;
            console.log(`  ✅ Application packaged in ${this.buildMetrics.packagingTime.toFixed(2)}ms`);
        } catch (error) {
            throw new Error(`Packaging failed: ${error.message}`);
        }
    }

    async validateBuild() {
        console.log('🔍 Validating build...');
        
        const expectedFiles = [
            'release/win-unpacked/Phantom Browser.exe',
            'release/win-unpacked/resources/app/dist/main.js',
            'release/win-unpacked/resources/app/renderer/index.html'
        ];

        for (const file of expectedFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Required file missing: ${file}`);
            }
        }

        // Calculate total size
        this.buildMetrics.totalSize = this.calculateDirectorySize('release/win-unpacked');
        console.log(`  ✅ Build validated - Size: ${(this.buildMetrics.totalSize / 1024 / 1024).toFixed(2)} MB`);
    }

    calculateDirectorySize(dirPath) {
        let totalSize = 0;
        
        if (!fs.existsSync(dirPath)) return 0;
        
        const files = fs.readdirSync(dirPath);
        
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stats = fs.statSync(filePath);
            
            if (stats.isDirectory()) {
                totalSize += this.calculateDirectorySize(filePath);
            } else {
                totalSize += stats.size;
            }
        }
        
        return totalSize;
    }

    generateBuildReport() {
        const totalTime = performance.now() - this.startTime;
        const sizeMB = (this.buildMetrics.totalSize / 1024 / 1024).toFixed(2);
        
        console.log('\n📊 BUILD REPORT');
        console.log('================');
        console.log(`⏱️  Total Build Time: ${totalTime.toFixed(2)}ms`);
        console.log(`🔨 TypeScript Compilation: ${this.buildMetrics.typeScriptTime.toFixed(2)}ms`);
        console.log(`⚡ Asset Optimization: ${this.buildMetrics.optimizationTime.toFixed(2)}ms`);
        console.log(`📦 Packaging: ${this.buildMetrics.packagingTime.toFixed(2)}ms`);
        console.log(`📁 Final Size: ${sizeMB} MB`);
        console.log(`🗑️  Excluded Files: ${this.buildMetrics.excludedFiles}`);
        console.log(`📍 Output: release/win-unpacked/`);
        
        // Performance assessment
        if (totalTime < 30000) {
            console.log('🎉 EXCELLENT: Build completed in under 30 seconds!');
        } else if (totalTime < 60000) {
            console.log('✅ GOOD: Build completed in under 1 minute');
        } else {
            console.log('⚠️  SLOW: Build took over 1 minute - consider optimization');
        }

        // Size assessment
        if (this.buildMetrics.totalSize < 200 * 1024 * 1024) {
            console.log('🎯 OPTIMAL: Build size under 200MB');
        } else if (this.buildMetrics.totalSize < 300 * 1024 * 1024) {
            console.log('✅ ACCEPTABLE: Build size under 300MB');
        } else {
            console.log('⚠️  LARGE: Build size over 300MB - consider optimization');
        }

        console.log('\n🚀 Phantom Browser build completed successfully!');
    }
}

// Run the optimized build
if (require.main === module) {
    const builder = new OptimizedBuilder();
    builder.build().catch(error => {
        console.error('Build failed:', error);
        process.exit(1);
    });
}

module.exports = OptimizedBuilder;
