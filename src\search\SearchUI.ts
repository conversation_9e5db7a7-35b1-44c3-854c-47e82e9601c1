import { SearchEngine, SearchProvider, SearchSuggestion } from './SearchEngine';

export class SearchUI {
    private searchEngine: SearchEngine;
    private suggestionContainer: HTMLElement | null = null;
    private currentSuggestionIndex = -1;
    private suggestions: SearchSuggestion[] = [];
    private debounceTimer: NodeJS.Timeout | null = null;

    constructor(searchEngine: SearchEngine) {
        this.searchEngine = searchEngine;
        this.initializeUI();
    }

    private initializeUI(): void {
        this.createSuggestionContainer();
        this.setupEventListeners();
        this.createSearchProviderSelector();
    }

    private createSuggestionContainer(): void {
        // Create suggestions dropdown
        this.suggestionContainer = document.createElement('div');
        this.suggestionContainer.id = 'searchSuggestions';
        this.suggestionContainer.className = 'search-suggestions hidden';
        this.suggestionContainer.innerHTML = `
            <div class="suggestions-list"></div>
        `;

        // Insert after address bar
        const addressBar = document.getElementById('addressBar');
        if (addressBar && addressBar.parentNode) {
            addressBar.parentNode.insertBefore(this.suggestionContainer, addressBar.nextSibling);
        }
    }

    private createSearchProviderSelector(): void {
        // Create search provider selector in privacy panel
        const privacyPanel = document.querySelector('.privacy-panel');
        if (!privacyPanel) return;

        const searchSection = document.createElement('div');
        searchSection.className = 'privacy-section';
        searchSection.innerHTML = `
            <h3>Search Engine</h3>
            <div class="search-provider-selector">
                <label for="searchProvider">Default Search Provider:</label>
                <select id="searchProvider" class="provider-select">
                    ${this.generateProviderOptions()}
                </select>
            </div>
            <div class="search-settings">
                <div class="setting-item">
                    <label class="toggle-switch" data-setting="searchSuggestions">
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="setting-label">Search Suggestions</span>
                </div>
                <div class="setting-item">
                    <label class="toggle-switch" data-setting="searchHistory">
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="setting-label">Search History</span>
                </div>
            </div>
            <div class="provider-info">
                <div id="providerDetails" class="provider-details"></div>
            </div>
        `;

        privacyPanel.appendChild(searchSection);
        this.updateProviderInfo();
        this.updateToggleStates();
    }

    private generateProviderOptions(): string {
        const providers = this.searchEngine.getProviders();
        const defaultProvider = this.searchEngine.getDefaultProvider();

        return providers.map(provider =>
            `<option value="${provider.id}" ${provider.id === defaultProvider.id ? 'selected' : ''}>
                ${provider.name} (Privacy: ${provider.privacyRating}/10)
            </option>`
        ).join('');
    }

    private updateProviderSelector(): void {
        const providerSelect = document.getElementById('searchProvider') as HTMLSelectElement;
        if (providerSelect) {
            const defaultProvider = this.searchEngine.getDefaultProvider();
            providerSelect.value = defaultProvider.id;
        }
    }

    private setupEventListeners(): void {
        const addressBar = document.getElementById('addressBar') as HTMLInputElement;
        if (!addressBar) return;

        // Input event for suggestions
        addressBar.addEventListener('input', (e) => {
            const target = e.target as HTMLInputElement;
            this.handleInput(target.value);
        });

        // Keydown for navigation
        addressBar.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });

        // Focus/blur events
        addressBar.addEventListener('focus', () => {
            this.showSuggestions();
        });

        addressBar.addEventListener('blur', () => {
            // Delay hiding to allow clicking on suggestions
            setTimeout(() => this.hideSuggestions(), 150);
        });

        // Provider selector
        const providerSelect = document.getElementById('searchProvider') as HTMLSelectElement;
        if (providerSelect) {
            providerSelect.addEventListener('change', (e) => {
                const target = e.target as HTMLSelectElement;
                this.searchEngine.setDefaultProvider(target.value);
                this.updateProviderInfo();
                this.updateProviderSelector();
            });
        }

        // Search settings toggles
        document.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            if (target.closest('[data-setting="searchSuggestions"]')) {
                this.toggleSearchSuggestions();
            } else if (target.closest('[data-setting="searchHistory"]')) {
                this.toggleSearchHistory();
            }
        });
    }

    private async handleInput(value: string): Promise<void> {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = setTimeout(async () => {
            if (value.length >= 2) {
                this.suggestions = await this.searchEngine.getSuggestions(value);
                this.renderSuggestions();
                this.showSuggestions();
            } else {
                this.hideSuggestions();
            }
        }, 300);
    }

    private handleKeyDown(e: KeyboardEvent): void {
        if (!this.suggestionContainer || this.suggestionContainer.classList.contains('hidden')) {
            return;
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.navigateSuggestions(1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.navigateSuggestions(-1);
                break;
            case 'Enter':
                e.preventDefault();
                this.selectCurrentSuggestion();
                break;
            case 'Escape':
                this.hideSuggestions();
                break;
        }
    }

    private navigateSuggestions(direction: number): void {
        const suggestionElements = this.suggestionContainer?.querySelectorAll('.suggestion-item');
        if (!suggestionElements || suggestionElements.length === 0) return;

        // Remove current highlight
        if (this.currentSuggestionIndex >= 0) {
            suggestionElements[this.currentSuggestionIndex].classList.remove('highlighted');
        }

        // Calculate new index
        this.currentSuggestionIndex += direction;
        if (this.currentSuggestionIndex < 0) {
            this.currentSuggestionIndex = suggestionElements.length - 1;
        } else if (this.currentSuggestionIndex >= suggestionElements.length) {
            this.currentSuggestionIndex = 0;
        }

        // Highlight new suggestion
        suggestionElements[this.currentSuggestionIndex].classList.add('highlighted');

        // Update address bar with suggestion
        const addressBar = document.getElementById('addressBar') as HTMLInputElement;
        if (addressBar) {
            addressBar.value = this.suggestions[this.currentSuggestionIndex].query;
        }
    }

    private selectCurrentSuggestion(): void {
        if (this.currentSuggestionIndex >= 0 && this.suggestions[this.currentSuggestionIndex]) {
            const suggestion = this.suggestions[this.currentSuggestionIndex];
            this.performSearch(suggestion.query);
        } else {
            // Use current address bar value
            const addressBar = document.getElementById('addressBar') as HTMLInputElement;
            if (addressBar) {
                this.performSearch(addressBar.value);
            }
        }
        this.hideSuggestions();
    }

    private performSearch(query: string): void {
        const result = this.searchEngine.processInput(query);
        
        // Update address bar
        const addressBar = document.getElementById('addressBar') as HTMLInputElement;
        if (addressBar) {
            addressBar.value = result.type === 'url' ? result.value : query;
        }

        // Navigate
        if ((window as any).phantomBrowserUI) {
            (window as any).phantomBrowserUI.navigate(result.value);
        }
    }

    private renderSuggestions(): void {
        if (!this.suggestionContainer) return;

        const suggestionsList = this.suggestionContainer.querySelector('.suggestions-list');
        if (!suggestionsList) return;

        suggestionsList.innerHTML = this.suggestions.map((suggestion, index) => `
            <div class="suggestion-item" data-index="${index}">
                <div class="suggestion-icon">
                    ${this.getSuggestionIcon(suggestion.type)}
                </div>
                <div class="suggestion-text">
                    <span class="suggestion-query">${this.escapeHtml(suggestion.query)}</span>
                    ${suggestion.title ? `<span class="suggestion-title">${this.escapeHtml(suggestion.title)}</span>` : ''}
                </div>
                <div class="suggestion-type">${suggestion.type}</div>
            </div>
        `).join('');

        // Add click listeners
        suggestionsList.querySelectorAll('.suggestion-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                this.currentSuggestionIndex = index;
                this.selectCurrentSuggestion();
            });
        });

        this.currentSuggestionIndex = -1;
    }

    private getSuggestionIcon(type: string): string {
        switch (type) {
            case 'history':
                return '🕒';
            case 'bookmark':
                return '⭐';
            case 'suggestion':
            default:
                return '🔍';
        }
    }

    private showSuggestions(): void {
        if (this.suggestionContainer && this.suggestions.length > 0) {
            this.suggestionContainer.classList.remove('hidden');
        }
    }

    private hideSuggestions(): void {
        if (this.suggestionContainer) {
            this.suggestionContainer.classList.add('hidden');
            this.currentSuggestionIndex = -1;
        }
    }

    private updateProviderInfo(): void {
        const provider = this.searchEngine.getDefaultProvider();
        const detailsElement = document.getElementById('providerDetails');
        
        if (detailsElement) {
            detailsElement.innerHTML = `
                <div class="provider-card">
                    <h4>${provider.name}</h4>
                    <p class="provider-description">${provider.description}</p>
                    <div class="provider-rating">
                        Privacy Rating: <span class="rating-value">${provider.privacyRating}/10</span>
                        <div class="rating-bar">
                            <div class="rating-fill" style="width: ${provider.privacyRating * 10}%"></div>
                        </div>
                    </div>
                    <div class="provider-features">
                        <strong>Features:</strong>
                        <ul>
                            ${provider.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }
    }

    private updateToggleStates(): void {
        const settings = this.searchEngine.getSettings();

        // Update search suggestions toggle
        const suggestionsToggle = document.querySelector('[data-setting="searchSuggestions"]');
        if (suggestionsToggle) {
            suggestionsToggle.classList.toggle('active', settings.enableSuggestions);
        }

        // Update search history toggle
        const historyToggle = document.querySelector('[data-setting="searchHistory"]');
        if (historyToggle) {
            historyToggle.classList.toggle('active', settings.enableHistory);
        }
    }

    private toggleSearchSuggestions(): void {
        const settings = this.searchEngine.getSettings();
        const newValue = !settings.enableSuggestions;

        this.searchEngine.updateSettings({ enableSuggestions: newValue });

        // Update toggle visual state
        const toggle = document.querySelector('[data-setting="searchSuggestions"]');
        if (toggle) {
            toggle.classList.toggle('active', newValue);
        }
    }

    private toggleSearchHistory(): void {
        const settings = this.searchEngine.getSettings();
        const newValue = !settings.enableHistory;

        this.searchEngine.updateSettings({ enableHistory: newValue });

        // Update toggle visual state
        const toggle = document.querySelector('[data-setting="searchHistory"]');
        if (toggle) {
            toggle.classList.toggle('active', newValue);
        }
    }

    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    public destroy(): void {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }
        
        if (this.suggestionContainer) {
            this.suggestionContainer.remove();
        }
    }
}
