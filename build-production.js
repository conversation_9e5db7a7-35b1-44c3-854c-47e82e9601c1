const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building Phantom Browser Production Release...');

// Step 1: Clean previous builds
console.log('📁 Cleaning previous builds...');
try {
    if (fs.existsSync('release')) {
        fs.rmSync('release', { recursive: true, force: true });
    }
    if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true });
    }
} catch (error) {
    console.log('⚠️  Warning: Could not clean all directories:', error.message);
}

// Step 2: Build TypeScript
console.log('🔨 Compiling TypeScript...');
try {
    execSync('npx tsc', { stdio: 'inherit' });
    console.log('✅ TypeScript compilation successful');
} catch (error) {
    console.error('❌ TypeScript compilation failed:', error.message);
    process.exit(1);
}

// Step 3: Create release directory structure
console.log('📦 Creating release structure...');
const releaseDir = path.join(__dirname, 'release');
const appDir = path.join(releaseDir, 'phantom-browser-win32-x64');

if (!fs.existsSync(releaseDir)) {
    fs.mkdirSync(releaseDir, { recursive: true });
}

if (!fs.existsSync(appDir)) {
    fs.mkdirSync(appDir, { recursive: true });
}

// Step 4: Use electron-builder for proper packaging
console.log('📦 Building with electron-builder...');
try {
    execSync('npm run dist:win', { stdio: 'inherit' });
    console.log('✅ Electron-builder packaging successful');

    // Move the built application to our release directory
    const electronBuilderOutput = path.join(__dirname, 'release', 'win-unpacked');
    if (fs.existsSync(electronBuilderOutput)) {
        const finalAppDir = path.join(releaseDir, 'phantom-browser-production-win32-x64');
        if (fs.existsSync(finalAppDir)) {
            fs.rmSync(finalAppDir, { recursive: true, force: true });
        }
        fs.renameSync(electronBuilderOutput, finalAppDir);

        // Update appDir reference for subsequent steps
        const newAppDir = finalAppDir;

        console.log(`📁 Production build moved to: ${newAppDir}`);

        // Calculate final size
        const totalSize = calculateSize(newAppDir);
        const sizeMB = (totalSize / (1024 * 1024)).toFixed(2);

        console.log('✅ Production build completed successfully!');
        console.log(`📊 Build size: ${sizeMB} MB`);
        console.log(`📁 Location: ${newAppDir}`);
        console.log('🎉 Ready for distribution!');

        return; // Skip the manual copying process
    }
} catch (error) {
    console.log('⚠️  Electron-builder failed, falling back to manual build...');
    console.error('Error:', error.message);
}

// Fallback: Manual file copying (only if electron-builder fails)
console.log('📋 Copying application files manually...');

// Copy only essential files for production
const filesToCopy = [
    { src: 'dist', dest: 'dist', required: true },
    { src: 'renderer', dest: 'renderer', required: true },
    { src: 'package.json', dest: 'package.json', required: true }
];

filesToCopy.forEach(({ src, dest, required }) => {
    const srcPath = path.join(__dirname, src);
    const destPath = path.join(appDir, dest);

    if (fs.existsSync(srcPath)) {
        if (fs.statSync(srcPath).isDirectory()) {
            fs.cpSync(srcPath, destPath, {
                recursive: true,
                filter: (src, dest) => {
                    // Exclude source maps and TypeScript definition files
                    if (src.endsWith('.map') || src.endsWith('.d.ts')) {
                        return false;
                    }
                    return true;
                }
            });
        } else {
            fs.copyFileSync(srcPath, destPath);
        }
        console.log(`✅ Copied ${src}`);
    } else if (required) {
        console.error(`❌ Required file/directory not found: ${src}`);
        process.exit(1);
    }
});

// Install only production dependencies
console.log('📚 Installing production dependencies...');
try {
    // Create a production package.json
    const originalPackage = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
    const productionPackage = {
        name: originalPackage.name,
        version: originalPackage.version,
        description: originalPackage.description,
        main: originalPackage.main,
        dependencies: originalPackage.dependencies
    };

    fs.writeFileSync(path.join(appDir, 'package.json'), JSON.stringify(productionPackage, null, 2));

    execSync('npm install --production --no-optional', {
        cwd: appDir,
        stdio: 'inherit'
    });
    console.log('✅ Production dependencies installed');
} catch (error) {
    console.error('❌ Failed to install production dependencies:', error.message);
    process.exit(1);
}

// Step 5: Create startup script
console.log('🎯 Creating startup script...');
const startupScript = `@echo off
echo Starting Phantom Browser...
cd /d "%~dp0"
node_modules\\.bin\\electron.cmd .
pause`;

fs.writeFileSync(path.join(appDir, 'start.bat'), startupScript);

// Step 6: Create README
console.log('📄 Creating documentation...');
const readme = `# Phantom Browser - Advanced Privacy Browser

## Installation
1. Extract all files to a directory
2. Double-click 'start.bat' to run the browser

## Features
- Advanced fingerprinting protection
- User agent rotation
- Traffic obfuscation
- Steganographic features
- Proxy and VPN support
- Real-time privacy monitoring

## System Requirements
- Windows 10 or later
- 4GB RAM minimum
- 500MB disk space

## Privacy Features
- Canvas, WebGL, and Audio fingerprinting protection
- Advanced ad and tracker blocking
- DNS over HTTPS
- Traffic pattern obfuscation
- Behavioral masking
- Request timing randomization

## Usage
The browser starts with maximum privacy protection enabled.
Use the privacy panel (gear icon) to adjust settings.

## Support
This is an advanced privacy browser designed for users who require
maximum anonymity and protection against tracking and surveillance.

Version: 1.0.0
Build Date: ${new Date().toISOString()}
`;

fs.writeFileSync(path.join(appDir, 'README.txt'), readme);

// Step 7: Create version info
const versionInfo = {
    version: '1.0.0',
    buildDate: new Date().toISOString(),
    features: [
        'Advanced Fingerprinting Protection',
        'User Agent Management',
        'Traffic Obfuscation',
        'Steganographic Features',
        'Proxy Integration',
        'Real-time Privacy Monitoring'
    ],
    platform: 'win32-x64',
    electron: '28.0.0'
};

fs.writeFileSync(path.join(appDir, 'version.json'), JSON.stringify(versionInfo, null, 2));

// Step 8: Calculate size
const calculateSize = (dirPath) => {
    let totalSize = 0;
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
            totalSize += calculateSize(filePath);
        } else {
            totalSize += stats.size;
        }
    });
    
    return totalSize;
};

const totalSize = calculateSize(appDir);
const sizeMB = (totalSize / (1024 * 1024)).toFixed(2);

console.log('✅ Production build completed successfully!');
console.log(`📊 Build size: ${sizeMB} MB`);
console.log(`📁 Location: ${appDir}`);
console.log('🎉 Ready for distribution!');

// Step 9: Create ZIP archive (optional)
console.log('🗜️  Creating ZIP archive...');
try {
    const archiver = require('archiver');
    const output = fs.createWriteStream(path.join(releaseDir, 'phantom-browser-v1.0.0-win32-x64.zip'));
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    output.on('close', () => {
        console.log(`📦 ZIP archive created: ${archive.pointer()} bytes`);
    });
    
    archive.on('error', (err) => {
        console.log('⚠️  Could not create ZIP archive:', err.message);
    });
    
    archive.pipe(output);
    archive.directory(appDir, 'phantom-browser');
    archive.finalize();
} catch (error) {
    console.log('⚠️  ZIP creation skipped (archiver not available)');
}

console.log('\n🎯 Build Summary:');
console.log(`   Version: 1.0.0`);
console.log(`   Platform: Windows x64`);
console.log(`   Size: ${sizeMB} MB`);
console.log(`   Location: ${appDir}`);
console.log(`   Startup: start.bat`);
console.log('\n🚀 Phantom Browser is ready for deployment!');
